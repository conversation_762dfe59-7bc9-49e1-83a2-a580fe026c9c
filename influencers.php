<?php
$pageTitle = 'المؤثرين والمراجعات';
require_once 'includes/header.php';

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$contentType = isset($_GET['type']) ? sanitizeInput($_GET['type']) : '';
$rating = isset($_GET['rating']) ? (int)$_GET['rating'] : 0;
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$sortBy = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'newest';

// بناء استعلام البحث
$whereConditions = ["ic.status = 'published'"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(ic.influencer_name LIKE ? OR ic.content_title LIKE ? OR ic.content_text LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if (!empty($contentType)) {
    $whereConditions[] = "ic.content_type = ?";
    $params[] = $contentType;
}

if ($rating > 0) {
    $whereConditions[] = "ic.rating >= ?";
    $params[] = $rating;
}

if ($category > 0) {
    $whereConditions[] = "ic.category_id = ?";
    $params[] = $category;
}

$whereClause = implode(' AND ', $whereConditions);

// ترتيب النتائج
$orderBy = "ic.created_at DESC";
switch ($sortBy) {
    case 'rating_high':
        $orderBy = "ic.rating DESC, ic.created_at DESC";
        break;
    case 'rating_low':
        $orderBy = "ic.rating ASC, ic.created_at DESC";
        break;
    case 'popular':
        $orderBy = "ic.views_count DESC, ic.created_at DESC";
        break;
    case 'name':
        $orderBy = "ic.influencer_name ASC";
        break;
}

// التصفح (Pagination)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 12;
$offset = ($page - 1) * $perPage;

// عدد المحتويات الإجمالي مع معالجة محسنة
try {
    $countSql = "SELECT COUNT(*) as total FROM influencers_content ic WHERE {$whereClause}";
    $totalResult = fetchOne($countSql, $params);
    $totalContent = ($totalResult && isset($totalResult['total'])) ? $totalResult['total'] : 0;
} catch (Exception $e) {
    // في حالة فشل الاستعلام، نجرب عد أساسي
    $basicCountSql = "SELECT COUNT(*) as total FROM influencers_content WHERE status = 'published'";
    $totalResult = fetchOne($basicCountSql);
    $totalContent = ($totalResult && isset($totalResult['total'])) ? $totalResult['total'] : 0;
}
$totalPages = ceil($totalContent / $perPage);

// جلب المحتوى مع معالجة محسنة للجداول المرتبطة
$sql = "
    SELECT ic.*,
           COALESCE(cc.name, '') as category_name,
           COALESCE(cc.name_ar, '') as category_name_ar,
           COALESCE(cc.icon, '') as category_icon,
           COALESCE(cc.color, '') as category_color,
           COALESCE(p.name, '') as product_name,
           COALESCE(p.price, 0) as product_price,
           COALESCE(p.image, '') as product_image,
           COALESCE(p.image_url_1, '') as image_url_1,
           COALESCE(p.image_url_2, '') as image_url_2,
           COALESCE(p.image_url_3, '') as image_url_3,
           COALESCE(p.image_url_4, '') as image_url_4,
           COALESCE(p.image_url_5, '') as image_url_5
    FROM influencers_content ic
    LEFT JOIN content_categories cc ON (ic.category_id = cc.id AND cc.status = 'active')
    LEFT JOIN products p ON (ic.product_id = p.id AND p.status = 'active')
    WHERE {$whereClause}
    ORDER BY {$orderBy}
    LIMIT {$perPage} OFFSET {$offset}
";

try {
    $contents = fetchAll($sql, $params);

    // إذا لم نحصل على نتائج، نجرب استعلام مبسط
    if (empty($contents) && $whereClause === "ic.status = 'published'") {
        $simpleSql = "
            SELECT ic.*,
                   '' as category_name,
                   '' as category_name_ar,
                   '' as category_icon,
                   '' as category_color,
                   '' as product_name,
                   0 as product_price,
                   '' as product_image,
                   '' as image_url_1,
                   '' as image_url_2,
                   '' as image_url_3,
                   '' as image_url_4,
                   '' as image_url_5
            FROM influencers_content ic
            WHERE ic.status = 'published'
            ORDER BY {$orderBy}
            LIMIT {$perPage} OFFSET {$offset}
        ";
        $contents = fetchAll($simpleSql);
    }
} catch (Exception $e) {
    // في حالة فشل الاستعلام، نجرب استعلام أساسي
    $basicSql = "SELECT * FROM influencers_content WHERE status = 'published' ORDER BY created_at DESC LIMIT {$perPage} OFFSET {$offset}";
    $contents = fetchAll($basicSql);

    // إضافة الحقول المفقودة
    foreach ($contents as &$content) {
        $content['category_name'] = '';
        $content['category_name_ar'] = '';
        $content['category_icon'] = '';
        $content['category_color'] = '';
        $content['product_name'] = '';
        $content['product_price'] = 0;
        $content['product_image'] = '';
        $content['image_url_1'] = '';
        $content['image_url_2'] = '';
        $content['image_url_3'] = '';
        $content['image_url_4'] = '';
        $content['image_url_5'] = '';
    }
}

// جلب التصنيفات للفلتر
$categories = fetchAll("SELECT id, name_ar as name FROM content_categories WHERE type IN ('influencer', 'both') AND status = 'active' ORDER BY name_ar");

// إحصائيات سريعة
$stats = [
    'total' => $totalContent,
    'videos' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE content_type = 'video' AND status = 'published'")['count'] ?? 0,
    'reviews' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE content_type = 'review' AND status = 'published'")['count'] ?? 0,
    'posts' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE content_type = 'post' AND status = 'published'")['count'] ?? 0
];

// دالة مساعدة لعرض الصور
function getInfluencerImage($content) {
    if ($content['influencer_image_type'] === 'url' && !empty($content['influencer_image'])) {
        return $content['influencer_image'];
    } elseif ($content['influencer_image_type'] === 'upload' && !empty($content['influencer_image'])) {
        return UPLOAD_URL . '/influencers/' . $content['influencer_image'];
    }
    return 'https://via.placeholder.com/150x150/f8f9fa/6c757d?text=' . urlencode('صورة المؤثر');
}

// دالة مساعدة لعرض صور المنتجات
function getProductImage($product) {
    // البحث عن أول صورة متاحة
    for ($i = 1; $i <= 5; $i++) {
        if (!empty($product['image_url_' . $i])) {
            return $product['image_url_' . $i];
        }
    }
    if (!empty($product['image'])) {
        return UPLOAD_URL . '/products/' . $product['image'];
    }
    return 'https://via.placeholder.com/100x100/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
}
?>

<!-- Page Header -->
<div class="bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="bi bi-people-fill"></i> المؤثرين والمراجعات
                </h1>
                <p class="lead mb-4">
                    اكتشف آراء وتجارب المؤثرين والعملاء مع منتجاتنا المميزة
                </p>
                <div class="d-flex gap-4">
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['total']; ?></div>
                        <div>إجمالي المحتوى</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['videos']; ?></div>
                        <div>فيديو</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['reviews']; ?></div>
                        <div>مراجعة</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['posts']; ?></div>
                        <div>منشور</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="bi bi-camera-video display-1 opacity-25"></i>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="container my-4">
    <div class="card shadow-sm">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <!-- البحث -->
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="ابحث في أسماء المؤثرين أو المحتوى...">
                    </div>
                </div>

                <!-- نوع المحتوى -->
                <div class="col-md-2">
                    <label for="type" class="form-label">نوع المحتوى</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">جميع الأنواع</option>
                        <option value="video" <?php echo $contentType === 'video' ? 'selected' : ''; ?>>فيديو</option>
                        <option value="review" <?php echo $contentType === 'review' ? 'selected' : ''; ?>>مراجعة</option>
                        <option value="post" <?php echo $contentType === 'post' ? 'selected' : ''; ?>>منشور</option>
                    </select>
                </div>

                <!-- التقييم -->
                <div class="col-md-2">
                    <label for="rating" class="form-label">التقييم الأدنى</label>
                    <select class="form-select" id="rating" name="rating">
                        <option value="0">جميع التقييمات</option>
                        <option value="5" <?php echo $rating === 5 ? 'selected' : ''; ?>>5 نجوم</option>
                        <option value="4" <?php echo $rating === 4 ? 'selected' : ''; ?>>4+ نجوم</option>
                        <option value="3" <?php echo $rating === 3 ? 'selected' : ''; ?>>3+ نجوم</option>
                        <option value="2" <?php echo $rating === 2 ? 'selected' : ''; ?>>2+ نجوم</option>
                        <option value="1" <?php echo $rating === 1 ? 'selected' : ''; ?>>1+ نجوم</option>
                    </select>
                </div>

                <!-- التصنيف -->
                <div class="col-md-2">
                    <label for="category" class="form-label">التصنيف</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" <?php echo $category === $cat['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- الترتيب -->
                <div class="col-md-2">
                    <label for="sort" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="newest" <?php echo $sortBy === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                        <option value="rating_high" <?php echo $sortBy === 'rating_high' ? 'selected' : ''; ?>>أعلى تقييم</option>
                        <option value="rating_low" <?php echo $sortBy === 'rating_low' ? 'selected' : ''; ?>>أقل تقييم</option>
                        <option value="popular" <?php echo $sortBy === 'popular' ? 'selected' : ''; ?>>الأكثر مشاهدة</option>
                        <option value="name" <?php echo $sortBy === 'name' ? 'selected' : ''; ?>>اسم المؤثر</option>
                    </select>
                </div>

                <!-- أزرار التحكم -->
                <div class="col-12">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-funnel"></i> تطبيق الفلاتر
                        </button>
                        <a href="influencers.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Content Grid -->
<div class="container my-5">
    <?php if (empty($contents)): ?>
        <!-- No Content Found -->
        <div class="text-center py-5">
            <i class="bi bi-camera-video-off display-1 text-muted mb-3"></i>
            <h3 class="text-muted">لا يوجد محتوى</h3>
            <p class="text-muted mb-4">لم يتم العثور على محتوى يطابق معايير البحث</p>
            <a href="influencers.php" class="btn btn-primary">
                <i class="bi bi-arrow-left"></i> عرض جميع المحتوى
            </a>
        </div>
    <?php else: ?>
        <!-- Content Cards -->
        <div class="row">
            <?php foreach ($contents as $content): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm border-0 content-card">
                        <!-- Content Header -->
                        <div class="card-header bg-white border-0 pb-0">
                            <div class="d-flex align-items-center">
                                <img src="<?php echo getInfluencerImage($content); ?>" 
                                     alt="<?php echo htmlspecialchars($content['influencer_name']); ?>"
                                     class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-bold"><?php echo htmlspecialchars($content['influencer_name']); ?></h6>
                                    <div class="d-flex align-items-center gap-2">
                                        <!-- Content Type Badge -->
                                        <?php
                                        $typeColors = [
                                            'video' => 'danger',
                                            'review' => 'warning',
                                            'post' => 'info'
                                        ];
                                        $typeNames = [
                                            'video' => 'فيديو',
                                            'review' => 'مراجعة',
                                            'post' => 'منشور'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $typeColors[$content['content_type']]; ?> rounded-pill">
                                            <?php echo $typeNames[$content['content_type']]; ?>
                                        </span>
                                        
                                        <!-- Category Badge -->
                                        <?php if ($content['category_name_ar']): ?>
                                            <span class="badge bg-light text-dark rounded-pill">
                                                <i class="<?php echo $content['category_icon']; ?>"></i>
                                                <?php echo htmlspecialchars($content['category_name_ar']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Content Body -->
                        <div class="card-body">
                            <!-- Title -->
                            <?php if ($content['content_title']): ?>
                                <h5 class="card-title"><?php echo htmlspecialchars($content['content_title']); ?></h5>
                            <?php endif; ?>

                            <!-- Rating -->
                            <?php if ($content['rating']): ?>
                                <div class="mb-2">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="bi bi-star<?php echo $i <= $content['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                    <?php endfor; ?>
                                    <span class="ms-2 text-muted">(<?php echo $content['rating']; ?>/5)</span>
                                </div>
                            <?php endif; ?>

                            <!-- Content Text -->
                            <p class="card-text">
                                <?php 
                                $text = strip_tags($content['content_text']);
                                echo htmlspecialchars(mb_substr($text, 0, 150) . (mb_strlen($text) > 150 ? '...' : ''));
                                ?>
                            </p>

                            <!-- Video Embed -->
                            <?php if ($content['content_type'] === 'video' && $content['video_url']): ?>
                                <div class="mb-3">
                                    <?php
                                    $videoUrl = $content['video_url'];
                                    $embedUrl = '';
                                    
                                    // YouTube
                                    if (strpos($videoUrl, 'youtube.com') !== false || strpos($videoUrl, 'youtu.be') !== false) {
                                        preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $videoUrl, $matches);
                                        if (isset($matches[1])) {
                                            $embedUrl = "https://www.youtube.com/embed/{$matches[1]}";
                                        }
                                    }
                                    ?>
                                    
                                    <?php if ($embedUrl): ?>
                                        <div class="ratio ratio-16x9">
                                            <iframe src="<?php echo $embedUrl; ?>" allowfullscreen></iframe>
                                        </div>
                                    <?php else: ?>
                                        <a href="<?php echo htmlspecialchars($videoUrl); ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-play-circle"></i> مشاهدة الفيديو
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Related Product -->
                            <?php if ($content['product_name']): ?>
                                <div class="border-top pt-3 mt-3">
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo getProductImage($content); ?>" 
                                             alt="<?php echo htmlspecialchars($content['product_name']); ?>"
                                             class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($content['product_name']); ?></h6>
                                            <div class="text-primary fw-bold"><?php echo number_format($content['product_price']); ?> دينار</div>
                                        </div>
                                        <a href="product.php?id=<?php echo $content['product_id']; ?>" class="btn btn-sm btn-outline-primary">
                                            عرض المنتج
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Content Footer -->
                        <div class="card-footer bg-white border-0">
                            <div class="d-flex justify-content-between align-items-center text-muted small">
                                <div>
                                    <i class="bi bi-eye"></i> <?php echo number_format($content['views_count']); ?> مشاهدة
                                </div>
                                <div>
                                    <i class="bi bi-calendar"></i> <?php echo date('Y/m/d', strtotime($content['created_at'])); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav aria-label="تصفح المحتوى" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="bi bi-chevron-right"></i> السابق
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                التالي <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.content-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.badge {
    font-size: 0.75em;
}

.ratio iframe {
    border-radius: 8px;
}
</style>

<script>
// تتبع المشاهدات
document.addEventListener('DOMContentLoaded', function() {
    // تسجيل مشاهدة الصفحة
    fetch('ajax/track_view.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'type=influencers_page'
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
