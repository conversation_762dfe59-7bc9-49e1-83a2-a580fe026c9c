<?php
$pageTitle = 'الرئيسية';
require_once 'includes/header.php';

// جلب المنتجات المميزة
$featuredProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.is_featured = 1 AND p.status = 'active' 
    ORDER BY p.created_at DESC 
    LIMIT 6
");

// جلب المنتجات الجديدة
$newProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.status = 'active' 
    ORDER BY p.created_at DESC 
    LIMIT 8
");

// جلب المنتجات المخفضة
$discountedProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.discount > 0 AND p.status = 'active' 
    ORDER BY p.discount DESC 
    LIMIT 6
");

// جلب آراء العملاء
$reviews = fetchAll("
    SELECT r.*, p.name as product_name 
    FROM reviews r 
    JOIN products p ON r.product_id = p.id 
    WHERE r.status = 'approved' 
    ORDER BY r.created_at DESC 
    LIMIT 6
");
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    مرحباً بك في <?php echo getSetting('site_name'); ?>
                </h1>
                <p class="lead mb-4">
                    اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار. 
                    نوفر لك تجربة تسوق مميزة مع توصيل سريع وآمن.
                </p>
                <div class="d-flex gap-3">
                    <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-light btn-lg">
                        <i class="bi bi-grid"></i> تصفح المنتجات
                    </a>
                    <a href="<?php echo SITE_URL; ?>/offers.php" class="btn btn-outline-light btn-lg">
                        <i class="bi bi-percent"></i> العروض الخاصة
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="https://via.placeholder.com/500x400/667eea/ffffff?text=<?php echo urlencode('متجرك الإلكتروني'); ?>"
                     alt="Hero Image" class="img-fluid rounded-3 shadow">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="bi bi-truck text-primary fs-1 mb-3"></i>
                        <h5>توصيل سريع</h5>
                        <p class="text-muted">توصيل مجاني للطلبات أكثر من <?php echo formatPrice(getSetting('free_delivery_threshold')); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="bi bi-shield-check text-success fs-1 mb-3"></i>
                        <h5>ضمان الجودة</h5>
                        <p class="text-muted">جميع منتجاتنا أصلية ومضمونة الجودة</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="bi bi-headset text-info fs-1 mb-3"></i>
                        <h5>دعم فني</h5>
                        <p class="text-muted">فريق دعم متاح على مدار الساعة لمساعدتك</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="bi bi-arrow-return-left text-warning fs-1 mb-3"></i>
                        <h5>سهولة الإرجاع</h5>
                        <p class="text-muted">إمكانية إرجاع المنتج خلال 14 يوم</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products -->
<?php if ($featuredProducts): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">المنتجات المميزة</h2>
            <p class="text-muted">اكتشف أفضل منتجاتنا المختارة خصيصاً لك</p>
        </div>
        
        <div class="row">
            <?php foreach ($featuredProducts as $product): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100 position-relative">
                        <?php if ($product['discount'] > 0): ?>
                            <span class="discount-badge">خصم <?php echo $product['discount']; ?>%</span>
                        <?php endif; ?>
                        
                        <?php
                        // البحث عن أول صورة متاحة من الصور الخمس
                        $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                        for ($i = 1; $i <= 5; $i++) {
                            if (!empty($product['image_url_' . $i])) {
                                $imageUrl = $product['image_url_' . $i];
                                break;
                            }
                        }
                        // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                        if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
                            $imageUrl = UPLOAD_URL . '/' . $product['image'];
                        }
                        ?>
                        <img src="<?php echo $imageUrl; ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>">
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                            </p>
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                            </p>
                            
                            <div class="price-section mb-3">
                                <?php if ($product['discount'] > 0): ?>
                                    <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                    <span class="price text-primary"><?php echo formatPrice($discountedPrice); ?></span>
                                    <span class="original-price ms-2"><?php echo formatPrice($product['price']); ?></span>
                                <?php else: ?>
                                    <span class="price text-primary"><?php echo formatPrice($product['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary flex-grow-1" onclick="addToCart(<?php echo $product['id']; ?>)">
                                    <i class="bi bi-cart-plus"></i> أضف للسلة
                                </button>
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                   class="btn btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                عرض جميع المنتجات <i class="bi bi-arrow-left"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Special Offers -->
<?php if ($discountedProducts): ?>
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold text-danger">العروض الخاصة</h2>
            <p class="text-muted">لا تفوت هذه الفرصة الذهبية للحصول على أفضل الخصومات</p>
        </div>
        
        <div class="row">
            <?php foreach ($discountedProducts as $product): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100 position-relative border-danger">
                        <span class="discount-badge">خصم <?php echo $product['discount']; ?>%</span>
                        
                        <?php
                        // البحث عن أول صورة متاحة من الصور الخمس
                        $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                        for ($i = 1; $i <= 5; $i++) {
                            if (!empty($product['image_url_' . $i])) {
                                $imageUrl = $product['image_url_' . $i];
                                break;
                            }
                        }
                        // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                        if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
                            $imageUrl = UPLOAD_URL . '/' . $product['image'];
                        }
                        ?>
                        <img src="<?php echo $imageUrl; ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>">
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                            </p>
                            
                            <div class="price-section mb-3">
                                <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                <span class="price text-danger fs-5"><?php echo formatPrice($discountedPrice); ?></span>
                                <span class="original-price ms-2"><?php echo formatPrice($product['price']); ?></span>
                                <div class="text-success small">
                                    وفر <?php echo formatPrice($product['price'] - $discountedPrice); ?>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button class="btn btn-danger flex-grow-1" onclick="addToCart(<?php echo $product['id']; ?>)">
                                    <i class="bi bi-cart-plus"></i> أضف للسلة
                                </button>
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                   class="btn btn-outline-danger">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Customer Reviews -->
<?php if ($reviews): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">آراء عملائنا</h2>
            <p class="text-muted">اقرأ تجارب عملائنا الراضين عن منتجاتنا وخدماتنا</p>
        </div>

        <div class="row">
            <?php foreach ($reviews as $review): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="bi bi-person text-white fs-4"></i>
                                </div>
                                <div class="ms-3">
                                    <h6 class="mb-0"><?php echo htmlspecialchars($review['customer_name']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($review['product_name']); ?></small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                <?php endfor; ?>
                            </div>

                            <p class="text-muted">
                                "<?php echo htmlspecialchars(substr($review['comment'], 0, 150)) . '...'; ?>"
                            </p>

                            <small class="text-muted">
                                <i class="bi bi-calendar"></i> <?php echo formatDate($review['created_at']); ?>
                            </small>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Newsletter Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="fw-bold mb-3">اشترك في النشرة البريدية</h3>
                <p class="mb-0">احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني</p>
            </div>
            <div class="col-lg-6">
                <form id="homeNewsletterForm" class="d-flex gap-2">
                    <input type="email" class="form-control" placeholder="بريدك الإلكتروني"
                           name="email" required>
                    <button type="submit" class="btn btn-light">
                        <i class="bi bi-send"></i> اشترك
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">لماذا تختار <?php echo getSetting('site_name'); ?>؟</h2>
            <p class="text-muted">نحن نقدم أفضل تجربة تسوق إلكتروني في المنطقة</p>
        </div>

        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-award text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5>جودة عالية</h5>
                        <p class="text-muted">نختار منتجاتنا بعناية فائقة لضمان أعلى مستويات الجودة والأصالة</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-lightning text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5>توصيل سريع</h5>
                        <p class="text-muted">نوصل طلباتك في أسرع وقت ممكن مع ضمان وصولها بحالة ممتازة</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-info rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-currency-dollar text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5>أسعار تنافسية</h5>
                        <p class="text-muted">نقدم أفضل الأسعار في السوق مع عروض وخصومات مستمرة</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-people text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5>خدمة عملاء متميزة</h5>
                        <p class="text-muted">فريق خدمة العملاء لدينا جاهز لمساعدتك في أي وقت</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-dark text-white">
    <div class="container text-center">
        <h2 class="fw-bold mb-3">ابدأ التسوق الآن!</h2>
        <p class="lead mb-4">اكتشف آلاف المنتجات المميزة واستمتع بتجربة تسوق لا تُنسى</p>
        <div class="d-flex justify-content-center gap-3">
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                <i class="bi bi-grid"></i> تصفح المنتجات
            </a>
            <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>"
               class="btn btn-success btn-lg" target="_blank">
                <i class="bi bi-whatsapp"></i> تواصل معنا
            </a>
        </div>
    </div>
</section>

<script>
// النشرة البريدية في الصفحة الرئيسية
document.getElementById('homeNewsletterForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('<?php echo SITE_URL; ?>/ajax/newsletter.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم الاشتراك بنجاح في النشرة البريدية!', 'success');
            this.reset();
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
