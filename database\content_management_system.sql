-- نظام إدارة المحتوى الشامل - قاعدة البيانات
-- Content Management System Database Schema

-- جدول فئات المحتوى (مشترك بين المؤثرين والإرشادات)
CREATE TABLE IF NOT EXISTS content_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    color VARCHAR(7) DEFAULT '#007bff',
    type ENUM('influencer', 'guideline', 'both') DEFAULT 'both',
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول محتوى المؤثرين والمراجعات
CREATE TABLE IF NOT EXISTS influencers_content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    influencer_name VARCHAR(255) NOT NULL,
    influencer_image VARCHAR(500),
    influencer_image_type ENUM('upload', 'url') DEFAULT 'upload',
    content_type ENUM('video', 'post', 'review') NOT NULL,
    content_title VARCHAR(500),
    content_text TEXT NOT NULL,
    rating TINYINT(1) CHECK (rating >= 1 AND rating <= 5),
    product_id INT,
    video_url VARCHAR(1000),
    video_platform ENUM('youtube', 'instagram', 'tiktok', 'other'),
    category_id INT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES content_categories(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_content_type (content_type),
    INDEX idx_rating (rating),
    INDEX idx_featured (is_featured),
    INDEX idx_published_at (published_at)
);

-- جدول الإرشادات والتعليمات
CREATE TABLE IF NOT EXISTS guidelines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) UNIQUE,
    category_id INT,
    icon_type ENUM('warning', 'info', 'tip', 'steps', 'danger', 'success') DEFAULT 'info',
    importance_level ENUM('normal', 'important', 'critical') DEFAULT 'normal',
    short_description TEXT,
    content TEXT NOT NULL,
    steps TEXT, -- JSON format for numbered steps
    images TEXT, -- JSON format for multiple images
    video_url VARCHAR(1000),
    difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
    usage_stage ENUM('pre_purchase', 'post_purchase', 'maintenance', 'general') DEFAULT 'general',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    views_count INT DEFAULT 0,
    helpful_count INT DEFAULT 0,
    not_helpful_count INT DEFAULT 0,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (category_id) REFERENCES content_categories(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_importance (importance_level),
    INDEX idx_difficulty (difficulty_level),
    INDEX idx_usage_stage (usage_stage),
    INDEX idx_featured (is_featured),
    INDEX idx_published_at (published_at)
);

-- جدول ربط الإرشادات بالمنتجات (علاقة متعدد لمتعدد)
CREATE TABLE IF NOT EXISTS guideline_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    guideline_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (guideline_id) REFERENCES guidelines(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_guideline_product (guideline_id, product_id)
);

-- جدول تتبع المشاهدات والتفاعل
CREATE TABLE IF NOT EXISTS content_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('influencer', 'guideline') NOT NULL,
    content_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(1000),
    session_id VARCHAR(255),
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_content (content_type, content_id),
    INDEX idx_viewed_at (viewed_at)
);

-- جدول سجل التغييرات
CREATE TABLE IF NOT EXISTS content_change_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('influencer', 'guideline', 'category') NOT NULL,
    content_id INT NOT NULL,
    action ENUM('create', 'update', 'delete', 'publish', 'unpublish') NOT NULL,
    old_data JSON,
    new_data JSON,
    admin_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_content (content_type, content_id),
    INDEX idx_action (action),
    INDEX idx_admin (admin_id),
    INDEX idx_created_at (created_at)
);

-- إدراج فئات افتراضية للمحتوى
INSERT INTO content_categories (name, name_ar, description, icon, color, type) VALUES
('Product Reviews', 'مراجعات المنتجات', 'مراجعات وتقييمات المنتجات من المؤثرين', 'bi-star-fill', '#ffc107', 'influencer'),
('Video Content', 'المحتوى المرئي', 'فيديوهات ومحتوى مرئي من المؤثرين', 'bi-play-circle-fill', '#dc3545', 'influencer'),
('Social Posts', 'المنشورات الاجتماعية', 'منشورات من وسائل التواصل الاجتماعي', 'bi-share-fill', '#17a2b8', 'influencer'),
('Usage Instructions', 'تعليمات الاستخدام', 'إرشادات حول كيفية استخدام المنتجات', 'bi-book-fill', '#28a745', 'guideline'),
('Safety Guidelines', 'إرشادات السلامة', 'تعليمات السلامة والأمان', 'bi-shield-fill-exclamation', '#fd7e14', 'guideline'),
('Maintenance Tips', 'نصائح الصيانة', 'إرشادات صيانة والعناية بالمنتجات', 'bi-tools', '#6f42c1', 'guideline'),
('Troubleshooting', 'حل المشاكل', 'إرشادات حل المشاكل الشائعة', 'bi-question-circle-fill', '#e83e8c', 'guideline');

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_influencers_search ON influencers_content(influencer_name, content_title);
CREATE INDEX idx_guidelines_search ON guidelines(title, short_description);
CREATE INDEX idx_content_categories_type ON content_categories(type, status);

-- إنشاء مشاهدات (Views) لتسهيل الاستعلامات
CREATE VIEW influencers_with_stats AS
SELECT 
    ic.*,
    cc.name as category_name,
    cc.name_ar as category_name_ar,
    cc.icon as category_icon,
    cc.color as category_color,
    p.name as product_name,
    p.price as product_price,
    p.image as product_image,
    COALESCE(cv.view_count, 0) as total_views
FROM influencers_content ic
LEFT JOIN content_categories cc ON ic.category_id = cc.id
LEFT JOIN products p ON ic.product_id = p.id
LEFT JOIN (
    SELECT content_id, COUNT(*) as view_count 
    FROM content_views 
    WHERE content_type = 'influencer' 
    GROUP BY content_id
) cv ON ic.id = cv.content_id;

CREATE VIEW guidelines_with_stats AS
SELECT 
    g.*,
    cc.name as category_name,
    cc.name_ar as category_name_ar,
    cc.icon as category_icon,
    cc.color as category_color,
    COALESCE(cv.view_count, 0) as total_views,
    GROUP_CONCAT(p.name SEPARATOR ', ') as related_products
FROM guidelines g
LEFT JOIN content_categories cc ON g.category_id = cc.id
LEFT JOIN guideline_products gp ON g.id = gp.guideline_id
LEFT JOIN products p ON gp.product_id = p.id
LEFT JOIN (
    SELECT content_id, COUNT(*) as view_count 
    FROM content_views 
    WHERE content_type = 'guideline' 
    GROUP BY content_id
) cv ON g.id = cv.content_id
GROUP BY g.id;
